/* TODO: 部分样式可以通过 theme token 覆盖，需要检查一下 */
/* antd 修改样式 */

/* Table */
.ant-table-wrapper {
  border: 1px solid #ebecf0;
  border-radius: 8px;
  overflow: hidden;
}
.ant-table-wrapper .ant-table .ant-table-footer {
  border-top: 1px solid #ebecf0;
  background: #fff;
}
.ant-table-wrapper
  .ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(
    .ant-table-row-expand-icon-cell
  ):not([colspan])::before {
  width: 0;
}
.ant-table-wrapper .ant-table-thead > tr > th {
  font-weight: 400;
}
.ant-table-wrapper,
.ant-table-wrapper > .ant-spin-nested-loading,
.ant-table-wrapper > .ant-spin-nested-loading > .ant-spin-container,
.ant-table-wrapper
  > .ant-spin-nested-loading
  > .ant-spin-container
  > .ant-table {
  height: 100%;
}
.ant-table-wrapper
  > .ant-spin-nested-loading
  > .ant-spin-container
  > .ant-table {
  display: flex;
  flex-direction: column;
}
.ant-table-wrapper .ant-table-container {
  flex: 1;
  min-height: 0;
}

/* Form */
.ant-form-item-label > label {
  font-weight: 500;
}
.ant-form.ant-form-vertical
  .ant-form-item
  .ant-form-item-label
  > label.ant-form-item-required::before {
  display: none;
}
.ant-form.ant-form-vertical
  .ant-form-item
  .ant-form-item-label
  > label.ant-form-item-required::after {
  display: inline-block;
  margin-inline-start: 4px;
  color: #ff5f4c;
  font-size: 14px;
  content: "*";
  visibility: visible;
}

/* Button */
.ant-btn.ant-btn-color-primary {
  background-color: #e6ebff;
  color: #0538ff;
}
.ant-btn.ant-btn-color-primary:not(:disabled):not(.ant-btn-disabled):hover {
  background-color: #0538ff33;
  color: #0538ff;
}
