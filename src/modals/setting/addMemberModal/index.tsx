import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import { Button, Form, Input, Modal, Select, TreeSelect } from "antd";
import { useEffect, useMemo } from "react";

import { useCreateUser } from "@/controllers/API/queries/tenant/useCreateUser";
import type { Organization, OrganizationUser, UserFormData } from "@/types/organization";

interface TreeSelectDataNode {
  title: string;
  value: string;
  key: string;
  children?: TreeSelectDataNode[];
}

export interface AddMemberModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (values: UserFormData) => void;
  data?: OrganizationUser;
  organizationTree?: Organization[];
}

export default function AddMemberModal({
  open,
  onCancel,
  onOk,
  data,
  organizationTree,
}: AddMemberModalProps) {
  const [form] = Form.useForm();

  const { mutateAsync: createUser, isPending } = useCreateUser();

  const isEdit = !!data;

  // 转换organizationTree为TreeSelect所需的格式
  const treeSelectData = useMemo(() => {
    const convertToTreeSelectData = (organizations: Organization[]): any[] => {
      return organizations.map((org) => ({
        title: org.name,
        value: org.id,
        key: org.id,
        children: org.children ? convertToTreeSelectData(org.children) : undefined,
      }));
    };

    return organizationTree ? convertToTreeSelectData(organizationTree) : [];
  }, [organizationTree]);

  const roleOptions = [
    { label: "成员", value: "normal" },
    { label: "管理员", value: "admin" },
    { label: "所有者", value: "owner" },
  ];

  useEffect(() => {
    if (!open) return;
    if (data) {
      form.setFieldsValue({
        nickname: data.nickname,
        email: data.email,
        phone: data.phone,
        department: "",
        role: "",
      });
    } else {
      form.resetFields();
    }
  }, [open, data, form]);

  const generatePassword = () => {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let password = "";
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    console.log("password:", password);
    form.setFieldsValue({ password });
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      await createUser({
        ...values,
        organization_id: "2fc30891-212d-4191-9be7-fc840bc14ec6",
      });
      form.resetFields();
      onOk(values);
    } catch (error) {
      console.log("表单验证失败:", error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={isEdit ? "编辑成员" : "添加成员"}
      open={open}
      width={400}
      className="add-member-modal"
      getContainer={false}
      okText="创建成员"
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={isPending}
    >
      <Form form={form} layout="vertical" className="mt-6">
        <Form.Item
          name="nickname"
          label="用户名"
          rules={[{ required: true, message: "请输入用户名" }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>
        <Form.Item
          name="email"
          label="邮箱"
          rules={[
            { required: true, message: "请输入邮箱" },
            { type: "email", message: "请输入有效的邮箱地址" },
          ]}
        >
          <Input placeholder="请输入邮箱" />
        </Form.Item>
        <Form.Item
          name="phone"
          label="手机号"
          rules={[
            { pattern: /^1[3-9]\d{9}$/, message: "请输入有效的手机号码" },
          ]}
        >
          <Input placeholder="请输入手机号" />
        </Form.Item>
        <Form.Item
          name="organization_id"
          label="部门"
          rules={[{ required: true, message: "请选择部门" }]}
        >
          <TreeSelect
            placeholder="请选择部门"
            treeData={treeSelectData}
            showSearch
            treeDefaultExpandAll
            allowClear
            style={{ width: '100%' }}
          />
        </Form.Item>
        <Form.Item
          name="role"
          label="角色"
          rules={[{ required: true, message: "请选择角色" }]}
        >
          <Select
            placeholder="请选择角色"
            options={roleOptions}
            disabled={isEdit}
          />
        </Form.Item>
        {!isEdit && (
          <Form.Item label="初始密码" className="mb-0" required>
            <div className="flex gap-2">
              <Form.Item
                name="password"
                rules={[
                  { required: true, message: "请输入初始密码" },
                  { min: 6, message: "密码长度至少6位" },
                ]}
                className="flex-1"
              >
                <Input.Password
                  placeholder="请输入初始密码"
                  iconRender={(visible) =>
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                />
              </Form.Item>
              <Button onClick={generatePassword}>生成</Button>
            </div>
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
}
