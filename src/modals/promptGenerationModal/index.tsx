import { Input, Select } from "antd";
import { useEffect, useRef, useState } from "react";

import AigcEmpty from "@/assets/aigc_empty.svg?react";
import AigcButton from "@/components/antd/aigcButton";
import { Button } from "@/components/main/button";
import { ShiningIcon } from "@/components/main/icon";
import Modal from "@/components/main/modal";
import Popconfirm from "@/components/main/popconfirm";
import { cn } from "@/utils/utils";

interface PromptGenerationModalProps {
  open: boolean;
  llmId: string;
  llmOptions: any[];
  setOpen: (open: boolean) => void;
  onSubmit: (prompt: string, welcomeMessage: string) => void;
}

// 预设的 placeholder 文案
const PLACEHOLDER_TEXTS = [
  "一个帮你写和纠错程序的机器人",
  "一个可以翻译多种语言的翻译器",
  "将会议内容提炼总结，包括讨论主题、关键要点和待办事项",
  "用地道的编辑技巧改进我的文章",
  "从长篇报告中提取洞察、识别风险并提炼关键信息",
  "一个可以让小白用户理解、使用和创建 Excel 公式的对话机器人",
  "旅行规划助手是一个智能工具，旨在帮助用户轻松规划他们的旅行",
  "把自然语言转换成 SQL 查询语句",
  "从用户提出的版本管理需求生成合适的 Git 命令",
];

export default function PromptGenerationModal({
  open,
  llmId,
  llmOptions,
  setOpen,
  onSubmit,
}: PromptGenerationModalProps) {
  const inputRef = useRef<any>(null);

  const [prompt, setPrompt] = useState("");
  const [welcomeMessage, setWelcomeMessage] = useState("");
  const [instruction, setInstruction] = useState("");
  const [currentPlaceholder, setCurrentPlaceholder] = useState("");
  const [selectedLlmId, setSelectedLlmId] = useState(llmId);

  useEffect(() => {
    if (open) {
      updatePlaceholder();
    } else {
      resetState();
    }
  }, [open]);

  useEffect(() => {
    setSelectedLlmId(llmId);
  }, [llmId]);

  // 随机选择 placeholder 文案
  const updatePlaceholder = () => {
    const randomIndex = Math.floor(Math.random() * PLACEHOLDER_TEXTS.length);
    setCurrentPlaceholder(PLACEHOLDER_TEXTS[randomIndex]);
  };

  const resetState = () => {
    setPrompt("");
    setWelcomeMessage("");
    setInstruction("");
    setCurrentPlaceholder("");
  };

  const handleInstructionChange = (value: string) => {
    if (!value) {
      updatePlaceholder();
    }
    setInstruction(value);
  };

  // 按下 Tab 键后将 placeholder 文案填充到输入框
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Tab" && !instruction) {
      e.preventDefault();
      setInstruction(currentPlaceholder);
    }
  };

  const handleGenerate = () => {
    // TODO: 生成 prompt 和 welcomeMessage
    setPrompt("## 角色 \n你是一个有用的助手");
    setWelcomeMessage("你好，有什么可以帮到你的吗？");
  };

  return (
    <Modal open={open} setOpen={setOpen} className="h-[700px] w-[900px]">
      <Modal.Header>角色配置生成器</Modal.Header>
      <Modal.Content className="flex flex-col gap-3 py-4">
        <div>
          <div className="mb-2 flex items-center justify-between">
            <div className="text-sm font-medium">指令描述</div>
            <Select
              className="w-[240px]"
              value={selectedLlmId}
              options={llmOptions}
              onChange={(value) => setSelectedLlmId(value)}
            />
          </div>
          <div className="relative">
            <Input.TextArea
              ref={inputRef}
              value={instruction}
              onChange={(e) => handleInstructionChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className={cn(
                "w-full !resize-none p-2 pr-[84px]",
                instruction ? "pl-3" : "pl-11",
              )}
              autoSize={{ minRows: 2, maxRows: 4 }}
            />
            {!instruction && (
              <div className="pointer-events-none absolute left-3 top-2 flex items-center gap-2 text-text-4">
                <span className="rounded border border-text-4 px-[3px] py-[1px] text-[9px]">
                  Tab
                </span>
                <span className="text-sm leading-6">{currentPlaceholder}</span>
              </div>
            )}
            <AigcButton
              className="absolute bottom-2 right-2"
              type="secondary"
              size="small"
              icon={<ShiningIcon className="dtc__icon" />}
              onClick={handleGenerate}
              disabled={!instruction}
            >
              生成
            </AigcButton>
          </div>
        </div>
        {prompt ? (
          <>
            <div className="flex flex-1 flex-col gap-1">
              <div className="text-sm font-medium">提示词</div>
              <Input.TextArea
                className="w-full flex-1 !resize-none bg-bg-light-3"
                value={prompt}
                readOnly
              />
            </div>
            <div className="flex flex-1 flex-col gap-1">
              <div className="text-sm font-medium">欢迎语</div>
              <Input.TextArea
                className="w-full flex-1 !resize-none bg-bg-light-3"
                value={welcomeMessage}
                readOnly
              />
            </div>
          </>
        ) : (
          <div className="flex flex-1 flex-col items-center justify-center">
            <AigcEmpty className="mb-2 h-[160px] w-[160px]" />
            <div className="text-sm text-text-3">请输入指令描述并点击生成</div>
          </div>
        )}
      </Modal.Content>
      <Modal.Footer className={cn({ hidden: !prompt || !welcomeMessage })}>
        <Popconfirm
          title="应用提示词将覆盖现有内容"
          placement="top"
          type="info"
          confirmText="确认应用"
          onConfirm={() => {
            setOpen(false);
            onSubmit(prompt, welcomeMessage);
          }}
        >
          <Button>应用</Button>
        </Popconfirm>
      </Modal.Footer>
    </Modal>
  );
}
