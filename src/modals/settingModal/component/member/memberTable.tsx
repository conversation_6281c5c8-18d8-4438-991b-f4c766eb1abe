import { SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Divider,
  Input,
  Pagination,
  Space,
  Table,
  TableProps,
} from "antd";
import { debounce } from "lodash";
import { useState } from "react";

import { Badge, BadgeVariant } from "@/components/main/badge";
import { useGetOrganizationUsers } from "@/controllers/API/queries/organization/useGetOrganizationUsers";
import { useModal } from "@/hooks/useModal";
import DeleteConfirmModal from "@/modals/deleteConfirmModal";
import AddMemberModal from "@/modals/setting/addMemberModal";
import MemberInfoModal from "@/modals/setting/memberInfoModal";
import MemberPasswordModal from "@/modals/setting/memberPasswordModal";
import {
  Organization,
  OrganizationNode,
  OrganizationUser,
  UserFormData,
} from "@/types/organization";
import { convertUTCToLocalTimezone } from "@/utils/utils";

interface MemberTableProps {
  currentOrganization?: OrganizationNode;
  organizationTree?: Organization[];
}

function MemberTable({
  currentOrganization,
  organizationTree,
}: MemberTableProps) {
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchValue, setSearchValue] = useState("");

  const addMemberModal = useModal<OrganizationUser>();
  const memberPasswordModal = useModal();
  const deleteModal = useModal<string>();
  const memberInfoModal = useModal<UserFormData>();

  const { data: pageData } = useGetOrganizationUsers({
    org_id: currentOrganization?.id?.startsWith("root_")
      ? undefined
      : currentOrganization?.id,
    page: current,
    size: pageSize,
    search: searchValue,
  });

  const columns: TableProps<OrganizationUser>["columns"] = [
    {
      title: "姓名",
      dataIndex: "nickname",
      key: "nickname",
      fixed: "left",
      width: 120,
    },
    {
      title: "邮箱",
      dataIndex: "email",
      key: "email",
      width: 200,
    },
    {
      title: "部门",
      dataIndex: "department",
      key: "department",
      width: 150,
    },
    {
      title: "角色",
      key: "role",
      dataIndex: "role",
      width: 100,
      render: (text) => <Badge>{text}</Badge>,
    },
    {
      title: "状态",
      key: "status",
      dataIndex: "status",
      width: 100,
      render: (status: "0" | "1") => (
        <Badge
          variant={status === "1" ? BadgeVariant.SUCCESS : BadgeVariant.WARN}
          showDot={true}
        >
          {status === "1" ? "有效" : "待激活"}
        </Badge>
      ),
    },
    {
      title: "加入时间",
      key: "create_at",
      dataIndex: "create_at",
      width: 180,
      render: (text) => convertUTCToLocalTimezone(text),
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size={0} split={<Divider type="vertical" />}>
          <Button
            type="link"
            size="small"
            className="p-0"
            onClick={() => addMemberModal.open(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            className="p-0"
            onClick={() => memberPasswordModal.open()}
          >
            改密
          </Button>
          <Button
            type="link"
            size="small"
            className="p-0"
            onClick={() => deleteModal.open(record.username)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleSearch = debounce((value: string) => {
    setSearchValue(value);
    // 这里可以添加搜索逻辑
  }, 500);

  const handleAddMember = () => {
    addMemberModal.open();
  };

  const handleAddMemberSubmit = (values: UserFormData) => {
    const isEdit = !!addMemberModal.data;
    console.log("添加成员:", values);
    addMemberModal.close();
    // 这里可以调用API添加成员

    if (!isEdit) {
      // 添加成功后打开账号信息弹窗
      memberInfoModal.open(values);
    }
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrent(page);
    if (size) {
      setPageSize(size);
    }
  };

  return (
    <div className="flex h-full w-full flex-col">
      {/* 表格头部 */}
      <div className="mb-2 flex h-6 items-center justify-between">
        <span className="text-base font-medium">
          {currentOrganization?.name}
        </span>
        <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
          <Input
            className="w-[240px]"
            placeholder="搜索提示..."
            prefix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
            onChange={(e) => handleSearch(e.target.value)}
            allowClear
            size="small"
          />
          <Button onClick={handleAddMember} size="small" className="text-xs">
            添加成员
          </Button>
        </div>
      </div>
      {/* 表格 */}
      <div className="flex min-h-0 flex-1 flex-col overflow-hidden rounded-md">
        <Table<OrganizationUser>
          columns={columns}
          dataSource={pageData?.items}
          scroll={{ x: 800, y: "calc(100% - 40px)" }}
          size="small"
          style={{
            backgroundColor: "#fff",
          }}
          pagination={false}
          footer={() => (
            <Pagination
              current={current}
              pageSize={pageSize}
              total={pageData?.total}
              onChange={handlePageChange}
              showSizeChanger={false}
              showQuickJumper={false}
              showTotal={(total) => `共 ${total} 条数据，每页 ${pageSize} 条`}
              size="small"
              align="end"
            />
          )}
        />
      </div>
      <AddMemberModal
        data={addMemberModal.data}
        open={addMemberModal.visible}
        onCancel={() => addMemberModal.close()}
        onOk={handleAddMemberSubmit}
        organizationTree={organizationTree}
      />
      <MemberPasswordModal
        open={memberPasswordModal.visible}
        onOk={() => memberPasswordModal.close()}
        onCancel={() => memberPasswordModal.close()}
      />
      <DeleteConfirmModal
        title="确认删除该成员吗？"
        description={`即将删除${deleteModal.data}，该操作不可逆，请谨慎操作！`}
        open={deleteModal.visible}
        setOpen={(open) => deleteModal.toggle(open)}
        onConfirm={() => {
          // TODO: 删除成员
          deleteModal.close();
        }}
      />
      <MemberInfoModal
        data={memberInfoModal.data}
        open={memberInfoModal.visible}
        onOk={() => memberInfoModal.close}
        onCancel={() => memberInfoModal.close()}
      />
    </div>
  );
}

export default MemberTable;
