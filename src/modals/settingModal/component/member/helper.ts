import { Organization, OrganizationNode } from "@/types/organization";

export function convertToOrganizationNode(org: Organization): OrganizationNode {
  if (!org) return org;
  return {
    id: org.id,
    name: org.name,
    children: org.children?.map((child) => convertToOrganizationNode(child)),
  };
}

export function convertToOrganizationNodes(
  orgs: Organization[],
): OrganizationNode[] {
  return orgs.map((org) => convertToOrganizationNode(org));
}
