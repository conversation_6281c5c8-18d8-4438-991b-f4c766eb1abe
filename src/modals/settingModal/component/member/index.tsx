import { useState } from "react";

import { useGetOrganizationTree } from "@/controllers/API/queries/organization/useGetOrganizationTree";
import { OrganizationNode } from "@/types/organization";
import MemberList from "./memberTable";
import OrganizationTree from "./organizationTree";
import TeamInfo from "./teamInfo";

function Member() {
  const [currentOrganization, setCurrentOrganization] = useState<
    OrganizationNode | undefined
  >(undefined);

  const { data: treeData } = useGetOrganizationTree({});

  return (
    <div className="flex h-full flex-col">
      <div className="flex h-[48px] items-center px-6 text-base font-medium">
        成员管理
      </div>
      <TeamInfo />
      <div className="flex min-h-0 flex-1 gap-4 p-6 pt-3">
        <div className="h-full w-[200px] flex-shrink-0">
          <OrganizationTree
            selectedNode={currentOrganization}
            onSelectNode={setCurrentOrganization}
            treeData={treeData}
          />
        </div>
        <div className="h-full min-w-0 flex-1">
          <MemberList
            currentOrganization={currentOrganization}
            organizationTree={treeData}
          />
        </div>
      </div>
    </div>
  );
}

export default Member;
