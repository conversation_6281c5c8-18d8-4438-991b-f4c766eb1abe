import { Dropdown, Input, type InputRef } from "antd";
import React, { useEffect, useRef, useState } from "react";

import {
  AddSubitemIcon,
  DownTriangleIcon,
  MoreIcon,
  TeamIcon,
  UserGroupIcon,
} from "@/components/main/icon";
import { useCreateOrganization } from "@/controllers/API/queries/organization/useCreateOrganization";
import { useDeleteOrganization } from "@/controllers/API/queries/organization/useDeleteOrganization";
import { useMoveOrganization } from "@/controllers/API/queries/organization/useMoveOrganization";
import { useUpdateOrganization } from "@/controllers/API/queries/organization/useUpdateOrganization";
import { useModal } from "@/hooks/useModal";
import DeleteConfirmModal from "@/modals/deleteConfirmModal";
import useAuthStore from "@/stores/authStore";
import type { Organization, OrganizationNode } from "@/types/organization";
import { cn } from "@/utils/utils";
import { convertToOrganizationNodes } from "./helper";

interface DragState {
  draggedNodeId: string | null;
  dragOverNodeId: string | null;
  dropPosition: "top" | "middle" | "bottom" | null;
}

interface OrganizationTreeProps {
  treeData?: Organization[];
  className?: string;
  selectedNode?: OrganizationNode;
  onSelectNode?: (node: OrganizationNode) => void;
}

const mockTreeData = {
  id: "root",
  name: "AIWorks 团队AIWorks",
  children: [
    {
      id: "tech",
      name: "技术部",
      children: [
        { id: "frontend", name: "前端小组" },
        { id: "backend", name: "后端小组" },
        { id: "test", name: "测试小组" },
      ],
    },
    {
      id: "product",
      name: "产品部",
      children: [
        { id: "design", name: "设计小组" },
        { id: "pm", name: "产品经理小组" },
      ],
    },
    {
      id: "market",
      name: "市场部",
      children: [{ id: "operation", name: "运营小组" }],
    },
  ],
};

export default function OrganizationTree({
  treeData: dataSource,
  className,
  selectedNode,
  onSelectNode,
}: OrganizationTreeProps) {
  const [treeData, setTreeData] = useState<OrganizationNode | undefined>();
  const [expandedKeys, setExpandedKeys] = useState<Set<string>>(
    new Set(["tech", "product", "market"]),
  );
  const [dragState, setDragState] = useState<DragState>({
    draggedNodeId: null,
    dragOverNodeId: null,
    dropPosition: null,
  });

  const inputRef = useRef<InputRef>(null);
  const deleteModal = useModal<OrganizationNode>();
  const userData = useAuthStore((state) => state.userData);

  const { mutate: createOrganization } = useCreateOrganization();
  const { mutate: updateOrganization } = useUpdateOrganization();
  const { mutate: deleteOrganization } = useDeleteOrganization();
  const { mutate: moveOrganization } = useMoveOrganization();

  useEffect(() => {
    if (userData?.current_tenant_id && userData?.current_tenant_name) {
      const rootNode: OrganizationNode = {
        id: `root_${userData?.current_tenant_id}`,
        name: userData?.current_tenant_name,
        children: convertToOrganizationNodes(dataSource || []),
      };
      setTreeData(rootNode);
      if (!selectedNode) {
        onSelectNode?.(rootNode);
      }
    }
  }, [dataSource]);

  // 更新节点
  const updateNode = (nodeId: string, updates: Partial<OrganizationNode>) => {
    if (!treeData) return;

    const updateNodeRecursive = (node: OrganizationNode): OrganizationNode => {
      if (node.id === nodeId) {
        return { ...node, ...updates };
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(updateNodeRecursive),
        };
      }
      return node;
    };
    setTreeData(updateNodeRecursive(treeData));

    if (nodeId.startsWith("new_")) {
      createOrganization({
        name: updates.name!,
        parent_id: nodeId.startsWith("new_root_")
          ? undefined
          : nodeId.split("_")[1],
        org_type: "department",
      });
    } else {
      updateOrganization({
        org_id: nodeId,
        name: updates.name!,
      });
    }
  };

  // 添加子节点
  const addChildNode = (parentId: string) => {
    if (!treeData) return;

    const newNodeId = `new_${parentId}_${Date.now()}`;
    const newNode: OrganizationNode = {
      id: newNodeId,
      name: "",
      isEditing: true,
    };

    const addNodeRecursive = (node: OrganizationNode): OrganizationNode => {
      if (node.id === parentId) {
        return {
          ...node,
          children: [...(node.children || []), newNode],
        };
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(addNodeRecursive),
        };
      }
      return node;
    };

    setTreeData(addNodeRecursive(treeData));
    setExpandedKeys((prev) => new Set(Array.from(prev).concat(parentId)));

    // 延迟聚焦到输入框
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);
  };

  // 删除节点
  const deleteNode = (nodeId: string) => {
    if (!treeData) return;

    const deleteNodeRecursive = (node: OrganizationNode): OrganizationNode => {
      if (!node) return node;
      if (node.children) {
        return {
          ...node,
          children: node.children
            .filter((child) => child.id !== nodeId)
            .map(deleteNodeRecursive),
        };
      }
      return node;
    };
    setTreeData(deleteNodeRecursive(treeData));
    deleteOrganization({ org_id: nodeId });
    deleteModal.close();
  };

  // 开始编辑
  const startEdit = (nodeId: string) => {
    updateNode(nodeId, { isEditing: true });
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);
  };

  // 保存编辑
  const saveEdit = (nodeId: string, newName: string) => {
    if (newName.trim()) {
      updateNode(nodeId, { name: newName.trim(), isEditing: false });
    } else {
      // 如果是新节点且名称为空，删除该节点
      if (nodeId.startsWith("new_")) {
        deleteNode(nodeId);
      } else {
        updateNode(nodeId, { isEditing: false });
      }
    }
  };

  // 取消编辑
  const cancelEdit = (nodeId: string) => {
    if (nodeId.startsWith("new_")) {
      deleteNode(nodeId);
    } else {
      updateNode(nodeId, { isEditing: false });
    }
  };

  // 切换展开状态
  const toggleExpanded = (nodeId: string) => {
    setExpandedKeys((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  // 查找节点及其父节点
  const findNodeWithParent = (
    nodeId: string,
    node: OrganizationNode | undefined = treeData,
    parent: OrganizationNode | null = null,
  ): { node: OrganizationNode; parent: OrganizationNode | null } | null => {
    if (!node) return null;
    if (node.id === nodeId) {
      return { node, parent };
    }
    if (node.children) {
      for (const child of node.children) {
        const result = findNodeWithParent(nodeId, child, node);
        if (result) return result;
      }
    }
    return null;
  };

  // 移动节点
  const moveNode = (
    draggedNodeId: string,
    targetNodeId: string,
    position: "top" | "middle" | "bottom",
  ) => {
    const draggedResult = findNodeWithParent(draggedNodeId);
    const targetResult = findNodeWithParent(targetNodeId);

    if (!draggedResult || !targetResult) return;

    const { node: draggedNode } = draggedResult;

    // 不能拖拽到自己或自己的子节点
    if (
      draggedNodeId === targetNodeId ||
      isDescendant(draggedNode, targetNodeId)
    ) {
      return;
    }

    // 从原位置移除
    const removeFromParent = (
      parentNode: OrganizationNode,
      childId: string,
    ) => {
      if (parentNode.children) {
        parentNode.children = parentNode.children.filter(
          (child) => child.id !== childId,
        );
      }
    };

    // 添加到新位置
    const addToParent = (
      parentNode: OrganizationNode,
      childNode: OrganizationNode,
      index?: number,
    ) => {
      if (!parentNode.children) {
        parentNode.children = [];
      }
      if (index !== undefined) {
        parentNode.children.splice(index, 0, childNode);
      } else {
        parentNode.children.push(childNode);
      }
    };

    setTreeData((prevTreeData) => {
      const newTreeData = JSON.parse(JSON.stringify(prevTreeData));

      // 重新查找节点（因为是新的对象）
      const newDraggedResult = findNodeWithParent(draggedNodeId, newTreeData);
      const newTargetResult = findNodeWithParent(targetNodeId, newTreeData);

      if (!newDraggedResult || !newTargetResult) return prevTreeData;

      const { node: newDraggedNode, parent: newDraggedParent } =
        newDraggedResult;
      const { node: newTargetNode, parent: newTargetParent } = newTargetResult;

      // 从原位置移除
      if (newDraggedParent) {
        removeFromParent(newDraggedParent, draggedNodeId);
      }

      let newParentId = "";

      if (position === "middle") {
        // 放到目标节点的子级
        addToParent(newTargetNode, newDraggedNode);
        // 展开目标节点
        setExpandedKeys(
          (prev) => new Set(Array.from(prev).concat(targetNodeId)),
        );
        newParentId = newTargetNode.id;
      } else {
        // 放到目标节点的同级
        if (newTargetParent) {
          const targetIndex =
            newTargetParent.children?.findIndex(
              (child) => child.id === targetNodeId,
            ) ?? -1;
          if (targetIndex !== -1) {
            const insertIndex =
              position === "top" ? targetIndex : targetIndex + 1;
            addToParent(newTargetParent, newDraggedNode, insertIndex);
            newParentId = newTargetParent.id;
          }
        }
      }

      if (newParentId) {
        moveOrganization({
          org_id: draggedNodeId,
          new_parent_id: newParentId,
        });
      }

      return newTreeData;
    });
  };

  // 检查是否为后代节点
  const isDescendant = (
    ancestorNode: OrganizationNode,
    nodeId: string,
  ): boolean => {
    if (!ancestorNode.children) return false;

    for (const child of ancestorNode.children) {
      if (child.id === nodeId || isDescendant(child, nodeId)) {
        return true;
      }
    }
    return false;
  };

  // 拖拽事件处理
  const handleDragStart = (e: React.DragEvent, nodeId: string) => {
    if (nodeId === "root") {
      e.preventDefault();
      return;
    }
    setDragState((prev) => ({ ...prev, draggedNodeId: nodeId }));
    e.dataTransfer.effectAllowed = "move";
  };

  const handleDragOver = (e: React.DragEvent, nodeId: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";

    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const y = e.clientY - rect.top;
    const height = rect.height;

    let position: "top" | "middle" | "bottom";
    if (y < height * 0.25) {
      position = "top";
    } else if (y > height * 0.75) {
      position = "bottom";
    } else {
      position = "middle";
    }

    setDragState((prev) => ({
      ...prev,
      dragOverNodeId: nodeId,
      dropPosition: position,
    }));
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // 只有当鼠标真正离开节点区域时才清除状态
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDragState((prev) => ({
        ...prev,
        dragOverNodeId: null,
        dropPosition: null,
      }));
    }
  };

  const handleDrop = (e: React.DragEvent, nodeId: string) => {
    e.preventDefault();

    if (dragState.draggedNodeId && dragState.dropPosition) {
      moveNode(dragState.draggedNodeId, nodeId, dragState.dropPosition);
    }

    setDragState({
      draggedNodeId: null,
      dragOverNodeId: null,
      dropPosition: null,
    });
  };

  const handleDragEnd = () => {
    setDragState({
      draggedNodeId: null,
      dragOverNodeId: null,
      dropPosition: null,
    });
  };

  // 节点选中处理
  const handleNodeClick = (nodeId: string) => {
    const result = findNodeWithParent(nodeId);
    if (result?.node) {
      onSelectNode?.(result?.node);
    }
  };

  // 渲染节点
  const renderNode = (node: OrganizationNode, level = 0): React.ReactNode => {
    const isExpanded = expandedKeys.has(node.id);
    const hasChildren = node.children && node.children.length > 0;
    const isRoot = level === 0;
    const isSelected = selectedNode?.id === node.id;
    const isDragging = dragState.draggedNodeId === node.id;
    const isDragOver = dragState.dragOverNodeId === node.id;
    const dropPosition = isDragOver ? dragState.dropPosition : null;

    const menuItems = [
      {
        key: "rename",
        label: "重命名",
        onClick: () => startEdit(node.id),
      },
      {
        key: "delete",
        label: "删除",
        onClick: () => {
          deleteModal.open(node);
        },
      },
    ];

    return (
      <div key={node.id} className="relative">
        {/* 拖拽指示器 */}
        {isDragOver && dropPosition === "top" && (
          <div
            className={cn(
              "absolute left-0 right-0 h-0.5 bg-primary-default",
              dropPosition === "top"
                ? "top-[-1px]"
                : dropPosition === "bottom" && "bottom-[-1px]",
            )}
          ></div>
        )}
        <div
          draggable={!isRoot}
          className={cn(
            "group relative flex min-h-8 w-full cursor-pointer items-center justify-between gap-2 rounded-lg border border-transparent px-2 py-[3px] leading-[22px] hover:bg-bg-light-3",
            isSelected && "!bg-bg-primary-1",
            isDragging && "opacity-50",
            isDragOver &&
              dropPosition === "middle" &&
              "border-blue-300 bg-blue-50",
          )}
          style={{
            paddingLeft: isRoot ? 8 : 8 + (level - 1) * 24,
          }}
          onClick={() => handleNodeClick(node.id)}
          onDragStart={(e) => handleDragStart(e, node.id)}
          onDragOver={(e) => handleDragOver(e, node.id)}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, node.id)}
          onDragEnd={handleDragEnd}
        >
          {/* 展开/收起图标 */}
          {hasChildren && !isRoot && (
            <span
              className="flex h-4 w-4 flex-shrink-0 items-center justify-center text-xs text-text-2"
              onClick={() => toggleExpanded(node.id)}
            >
              <DownTriangleIcon
                className={cn(
                  "transition-transform",
                  isExpanded ? "" : "-rotate-90",
                )}
              />
            </span>
          )}
          {/* 没有子节点时，展开/收起图标的占位 */}
          {!hasChildren && !isRoot && <div className="h-4 w-4"></div>}
          {node.isEditing ? (
            <Input
              ref={inputRef}
              defaultValue={node.name}
              placeholder="组织名称"
              className="h-7 min-w-0 flex-1 text-sm"
              onPressEnter={(e) => {
                const target = e.target as HTMLInputElement;
                saveEdit(node.id, target.value);
              }}
              onBlur={(e) => {
                saveEdit(node.id, e.target.value);
              }}
              onKeyDown={(e) => {
                if (e.key === "Escape") {
                  cancelEdit(node.id);
                }
              }}
            />
          ) : (
            <>
              <div className="flex-shrink-0 text-base text-primary-default">
                {isRoot ? <TeamIcon /> : <UserGroupIcon />}
              </div>
              <div className={cn("min-w-0 flex-1 truncate text-sm")}>
                {node.name}
              </div>
              <div className="hidden flex-shrink-0 items-center gap-1 transition-opacity duration-200 group-hover:flex">
                <span
                  className="flex h-6 w-6 items-center justify-center rounded text-sm text-text-2 transition-all hover:bg-gray-100"
                  onClick={(e) => {
                    e.stopPropagation();
                    addChildNode(node.id);
                  }}
                >
                  <AddSubitemIcon />
                </span>
                <Dropdown
                  className={cn(isRoot && "hidden")}
                  menu={{ items: menuItems }}
                  trigger={["click"]}
                  placement="bottomRight"
                  getPopupContainer={(trigger) =>
                    trigger.parentNode as HTMLElement
                  }
                >
                  <span
                    className="flex h-6 w-6 items-center justify-center rounded text-sm text-text-2 transition-all duration-200 hover:bg-gray-100"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreIcon />
                  </span>
                </Dropdown>
              </div>
            </>
          )}
        </div>
        {/* 子节点 */}
        {hasChildren && (isExpanded || isRoot) && (
          <div>
            {node.children?.map((child) => renderNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("flex h-full w-full flex-col gap-2", className)}>
      <h3 className="text-base font-medium">组织架构</h3>
      <div className="min-h-0 flex-1 overflow-y-auto">
        {treeData && renderNode(treeData)}
      </div>
      <DeleteConfirmModal
        title="确认删除该对组织吗？"
        description={`即将删除${deleteModal.data?.name}，该操作不可逆，请谨慎操作！`}
        open={deleteModal.visible}
        setOpen={(open) => deleteModal.toggle(open)}
        onConfirm={() =>
          deleteModal.data?.id && deleteNode(deleteModal.data?.id)
        }
      />
    </div>
  );
}
