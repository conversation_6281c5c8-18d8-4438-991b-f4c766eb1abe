import {
  BASE_URL_API,
  BASE_URL_API_V2,
  BASE_URL_API_V3,
} from "../../../constants/constants";

export const URLs = {
  TRANSACTIONS: `monitor/transactions`,
  API_KEY: `api_key`,
  FILES: `files`,
  FILE_MANAGEMENT: `files`,
  VERSION: `version`,
  MESSAGES: `monitor/messages`,
  BUILDS: `monitor/builds`,
  STORE: `store`,
  USERS: "users",
  USER: "user",
  LOGOUT: `auth/logout`,
  LOGIN: `auth/login`,
  REFRESH: "auth/refresh",
  BUILD: `build`,
  CUSTOM_COMPONENT: `custom_component`,
  FLOWS: `flows`,
  FOLDERS: `folders`,
  VARIABLES: `variables`,
  VALIDATE: `validate`,
  CONFIG: `config`,
  STARTER_PROJECTS: `starter-projects`,
  SIDEBAR_CATEGORIES: `sidebar_categories`,
  ALL: `all`,
  VOICE: `voice`,
  PUBLIC_FLOW: `flows/public_flow`,

  /** 知识库 */
  GET_KNOWLEDGES: `kb/list`,
  CREATE_KNOWLEDGE: `kb/create`,
  UPDATE_KNOWLEDGE: `kb/update`,
  DELETE_KNOWLEDGE: `kb/rm`,
  GET_DOCUMENTS: `document/list`,
  UPLOAD_DOCUMENT: `document/upload`,
  DELETE_DOCUMENT: `document/rm`,
  RENAME_DOCUMENT: `document/rename`,
  RUN_DOCUMENT: `document/run`,
  CHANGE_DOCUMENT_STATUS: `document/change_status`,
  GET_YUQUE_DIRECTORY: `yuque/repos`,
  // TODO: 需要支持路径中间传递参数，完整路径为 yuque/repos/{repo_id}/toc，
  GET_YUQUE_DIRECTORY_NODES: `yuque/repos`,
  ADD_YUQUE_FILE: `yuque/manual_upload`,

  /** 应用管理 */
  CREATE_APP: `apps/`,
  GET_APPS: `apps/`,
  UPDATE_APP: `apps`,
  DELETE_APP: `apps`,
  GET_APP_DETAIL: `apps`,
  APP_CHAT: `apps`,
  GET_CONVERSATIONS: `apps`,
  GET_CONVERSATION_DETAIL: `apps`,
  DELETE_CONVERSATION: `apps`,

  /** 组织架构 */
  GET_ORGANIZATION_TREE: `organization/tree`,
  CREATE_ORGANIZATION: `organization`,
  UPDATE_ORGANIZATION: `organization`,
  DELETE_ORGANIZATION: `organization`,
  MOVE_ORGANIZATION: `organization`,
  GET_ORGANIZATION_USERS: `organization/users`,

  /** 租户 */
  CREATE_USER: `tenant/users`,
} as const;

export function getURL(
  key: keyof typeof URLs,
  params: Record<string, string | number | boolean> = {},
  v2 = false,
  v3 = false,
) {
  let url = URLs[key];
  Object.keys(params).forEach((key) => (url += `/${params[key]}`));
  return `${v3 ? BASE_URL_API_V3 : v2 ? BASE_URL_API_V2 : BASE_URL_API}${url.toString()}`;
}

export type URLsType = typeof URLs;
