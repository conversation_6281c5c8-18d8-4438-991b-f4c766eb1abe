import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import type { Organization } from "@/types/organization";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  name: string;
  org_type: "department";
  parent_id?: string;
  description?: string;
  sort_order?: number;
}

export const useCreateOrganization: useMutationFunctionType<
  undefined,
  RequestParams,
  Organization
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const createOrganizationFn = async (
    payload: RequestParams,
  ): Promise<Organization> => {
    const url = getURL("CREATE_ORGANIZATION", {}, false, true);
    const res = await api.post<ResponseType<Organization>>(url, payload);
    return res.data?.data;
  };

  const mutation = mutate(["useCreateOrganization"], createOrganizationFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetOrganizationTree"],
      });
    },
  });

  return mutation;
};
