import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import type { Organization } from "@/types/organization";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  org_id: string;
  name?: string;
  org_type?: "department";
  description?: string;
  sort_order?: number;
  status?: string;
}

export const useUpdateOrganization: useMutationFunctionType<
  undefined,
  RequestParams,
  Organization
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const updateOrganizationFn = async (
    payload: RequestParams,
  ): Promise<Organization> => {
    const { org_id, ...rest } = payload;
    const url = getURL("UPDATE_ORGANIZATION", { org_id }, false, true);
    const res = await api.put<ResponseType<Organization>>(url, rest);
    return res.data?.data;
  };

  const mutation = mutate(["useUpdateOrganization"], updateOrganizationFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetOrganizationTree"],
      });
    },
  });

  return mutation;
};
