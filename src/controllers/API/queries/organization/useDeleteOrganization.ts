import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  org_id: string;
}

export const useDeleteOrganization: useMutationFunctionType<
  undefined,
  RequestParams,
  string
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const deleteOrganizationFn = async (
    payload: RequestParams,
  ): Promise<string> => {
    const url = getURL(
      "DELETE_ORGANIZATION",
      { org_id: payload.org_id },
      false,
      true,
    );
    const res = await api.delete<ResponseType<string>>(url);
    return res.data?.data;
  };

  const mutation = mutate(["useDeleteOrganization"], deleteOrganizationFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetOrganizationTree"],
      });
    },
  });

  return mutation;
};
