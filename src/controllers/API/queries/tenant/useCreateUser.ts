import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import type { OrganizationUser, UserFormData } from "@/types/organization";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

export const useCreateUser: useMutationFunctionType<
  undefined,
  UserFormData,
  OrganizationUser
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const createUserFn = async (
    payload: UserFormData,
  ): Promise<OrganizationUser> => {
    const url = getURL("CREATE_USER", {}, false, true);
    const res = await api.post<ResponseType<OrganizationUser>>(url, payload);
    return res.data?.data;
  };

  const mutation = mutate(["useCreateUser"], createUserFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetOrganizationUsers"],
      });
    },
  });

  return mutation;
};
