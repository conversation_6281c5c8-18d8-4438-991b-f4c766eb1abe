import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useMutationFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RunDocumentParams {
  doc_ids: string[];
  run?: number;
  delete?: boolean;
}

type RunDocumentReturnType = string;

const addQueryParams = (
  url: string,
  params: Pick<RunDocumentParams, "run" | "delete">,
): string => {
  return buildQueryStringUrl(url, params);
};

export const useRunDocument: useMutationFunctionType<
  undefined,
  RunDocumentParams,
  RunDocumentReturnType
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const runDocumentFn = async (
    payload: RunDocumentParams,
  ): Promise<RunDocumentReturnType> => {
    const url = addQueryParams(getURL("RUN_DOCUMENT", {}, false, true), {
      run: payload.run || 1,
      delete: payload.delete || false,
    });

    const { data: responseData } = await api.post<
      ResponseType<RunDocumentReturnType>
    >(url, payload.doc_ids);

    return responseData.data;
  };

  const mutation = mutate(["useRunDocument"], runDocumentFn, {
    retry: false,
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetDocuments"],
      });
    },
  });

  return mutation;
};
