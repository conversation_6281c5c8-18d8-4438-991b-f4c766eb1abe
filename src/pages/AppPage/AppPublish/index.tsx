import { Input } from "antd";

import Copy from "@/components/antd/copy";
import { Badge, BadgeVariant } from "@/components/main/badge";
import { Button } from "@/components/main/button";
import { CopyIcon } from "@/components/main/icon";
import SubPageLayout from "@/components/main/layout/components/subPageLayout";
import { useGetAppChatLink } from "@/hooks/app/useGetAppChatLink";

export default function AppPublish() {
  const chatLink = useGetAppChatLink();

  return (
    <SubPageLayout title="应用发布" backPath="/app">
      <div className="mx-auto h-full max-w-[800px] overflow-y-auto py-10">
        <div className="mb-6 rounded-lg border border-solid border-border-1 p-4">
          <div className="mb-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="text-base font-medium">应用状态</div>
              <Badge variant={BadgeVariant.SUCCESS} showDot>
                已发布
              </Badge>
            </div>
            <Button variant="destructive" disabled>
              下线
            </Button>
          </div>
          <div className="text-sm text-text-3">
            应用已发布，用户可以通过以下方式访问。
          </div>
        </div>
        <div className="rounded-lg border border-solid border-border-1 p-4">
          <div className="mb-4 text-base font-medium">发布方式</div>
          <div className="mb-2 text-sm font-medium">公共链接</div>
          <Input
            className="mb-2"
            value={chatLink}
            suffix={
              <Copy
                button={
                  <CopyIcon className="cursor-pointer text-sm text-text-2" />
                }
                text={chatLink}
              />
            }
            readOnly
          />
          <div className="text-sm text-text-3">
            公共访问已启用，任何人都可以通过上述链接访问你的应用
          </div>
        </div>
      </div>
    </SubPageLayout>
  );
}
