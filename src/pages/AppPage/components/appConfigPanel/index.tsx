import { Button, Form, Input, Select, Switch, type FormInstance } from "antd";
import { debounce } from "lodash";
import { useEffect } from "react";
import { useParams } from "react-router-dom";

import AigcButton from "@/components/antd/aigcButton";
import { EditIcon, ShiningIcon, SquarePlusIcon } from "@/components/main/icon";
import { useModal } from "@/hooks/useModal";
import NewAppModal from "@/modals/newAppModal";
import PromptGenerationModal from "@/modals/promptGenerationModal";
import RelateKnowledgeModal from "@/modals/relateKnowledge";
import { LlmModelType } from "@/ragflow/constants/knowledge";
import { useFetchSystemModelSettingOnMount } from "@/ragflow/pages/user-setting/setting-model/hooks";
import type { App, AppConfigFormData } from "@/types/app";
import type { KnowledgeType } from "@/types/knowledge";
import { getColorFromString } from "@/utils/styleUtils";
import { cn } from "@/utils/utils";
import { transformToAppData, transformToFormData } from "../../utils";
import KnowledgeConfig from "../knowledgeConfig";

const { TextArea } = Input;

interface AppConfigPanelProps {
  data: any;
  form: FormInstance;
  onValuesChange: (
    values: any,
    immediateUpdate?: boolean,
    isEffectSaveStatus?: boolean,
  ) => void;
}

function AppConfigPanel({ data, form, onValuesChange }: AppConfigPanelProps) {
  const { appId } = useParams();
  const newAppModal = useModal<App>();
  const relateKnowledgeModal = useModal();
  const promptGenerationModal = useModal();

  const { systemSetting, allOptions } = useFetchSystemModelSettingOnMount();

  const color = getColorFromString(appId || "");

  useEffect(() => {
    if (data?.id) {
      const model = data.model || systemSetting.llm_id;
      updateFieldsValue({ ...data, model }, false);
    }
  }, [data?.id, systemSetting.llm_id]);

  // 添加知识库
  const addKnowledgeBase = () => {
    relateKnowledgeModal.open();
  };

  const handleRelateKnowledge = (selectedDatasets: KnowledgeType[]) => {
    const prevDatasets = form.getFieldValue("datasets") || [];
    const newDatasets = selectedDatasets.map((dataset) => ({
      id: dataset.id,
      name: dataset.name,
      similarityThreshold: 0.5,
      vectorSimilarityWeight: 0.5,
      topN: 10,
      topK: 50,
      useRerank: true,
      rerankModel: "BGE Rerank v2-M3",
    }));
    const nextDatasets = [...prevDatasets, ...newDatasets];

    updateFieldsValue({
      datasets: nextDatasets,
    });
  };

  const handleApplyPrompt = (prompt: string, openingStatement: string) => {
    updateFieldsValue({
      prompt,
      openingStatement,
    });
  };

  const handleFormValuesChange = (
    changedValues: Partial<AppConfigFormData>,
    values: AppConfigFormData,
  ) => {
    const needRerenderFields = ["speechToText", "textToSpeech"];
    const immediateUpdate = Object.keys(changedValues).some((key) =>
      needRerenderFields.includes(key),
    );
    onValuesChange(values, immediateUpdate);
  };

  const updateFieldsValue = (
    values: Partial<AppConfigFormData>,
    isEffectSaveStatus = true,
  ) => {
    form.setFieldsValue(values);
    onValuesChange(values, true, isEffectSaveStatus);
  };

  const handleUpdateSuccess = (data: App) => {
    updateFieldsValue(transformToFormData(data));
  };

  const updateOpeningStatement = debounce((value: string) => {
    updateFieldsValue({ openingStatement: value });
  }, 300);

  return (
    <div className="border-r-solid flex h-full w-[400px] flex-col gap-6 overflow-y-auto border-r border-border-1 p-6">
      <div className="flex items-center gap-2">
        <div
          className={cn(
            `flex h-8 w-8 items-center justify-center rounded-lg bg-bg-light-3`,
            color,
          )}
        >
          <span className="text-xl">✨</span>
        </div>
        <div className="font-medium">{data?.name}</div>
        <EditIcon
          className="cursor-pointer text-xl text-text-2"
          onClick={() => newAppModal.open(transformToAppData(data) as App)}
        />
      </div>
      <Form
        form={form}
        layout="vertical"
        className="text-text-1-primary"
        onValuesChange={handleFormValuesChange}
      >
        <div className="mb-3 text-base font-medium">模型配置</div>
        <Form.Item name="model" label="模型选择" className="mb-3">
          <Select
            className="w-full"
            options={allOptions[LlmModelType.Chat]}
            allowClear
            showSearch
          />
        </Form.Item>
        {/* 暂时隐藏，后续再支持 */}
        {/* <Form.Item name="memoryRounds" className="mb-3" noStyle>
          <SliderWithInput min={0} max={100} step={1} label="聊天记忆轮次" />
        </Form.Item> */}
        <div className="mb-3 mt-6 flex items-center justify-between">
          <span className="text-base font-medium">角色配置</span>
          {/* 暂时隐藏，后续再支持 */}
          {/* <AigcButton
            type="secondary"
            icon={<ShiningIcon className="dtc__icon" />}
            size="small"
            onClick={() => promptGenerationModal.open()}
          >
            一键生成
          </AigcButton> */}
        </div>
        <Form.Item name="prompt" label="提示词" className="mb-3">
          <TextArea
            className="max-h-[240px] !min-h-[60px]"
            placeholder="请输入提示词, 最长120字符"
            maxLength={120}
          />
        </Form.Item>
        <Form.Item name="openingStatement" label="欢迎语" className="mb-3">
          <TextArea
            className="max-h-[240px] !min-h-[60px]"
            placeholder="请输入欢迎语, 最长120字符"
            maxLength={120}
            onChange={(e) => {
              updateOpeningStatement(e.target.value);
            }}
          />
        </Form.Item>
        <div className="mb-3 mt-6 flex items-center justify-between">
          <span className="text-base font-medium">关联知识库</span>
          <Button
            size="small"
            icon={<SquarePlusIcon className="text-sm" />}
            onClick={addKnowledgeBase}
            className="gap-0.5 text-xs"
          >
            添加
          </Button>
        </div>
        <Form.List name="datasets">
          {(fields, { remove }) => {
            const datasets = form.getFieldValue("datasets") || [];
            return fields.map((field) => (
              <KnowledgeConfig
                key={field.name}
                fieldName={field.name}
                parentFieldName="datasets"
                knowledgeName={datasets[field.name].name}
                onRemove={() => remove(field.name)}
              />
            ));
          }}
        </Form.List>
        <div className="mb-[6px] mt-6 text-base font-medium">高级配置</div>
        <Form.Item
          name="speechToText"
          label="语音输入"
          className="mb-[2px]"
          valuePropName="checked"
          layout="horizontal"
          labelCol={{ span: 21 }}
          wrapperCol={{ span: 3 }}
          labelAlign="left"
          colon={false}
        >
          <Switch />
        </Form.Item>
        <Form.Item
          name="textToSpeech"
          label="语音播放"
          className="mb-[2px]"
          valuePropName="checked"
          layout="horizontal"
          labelCol={{ span: 21 }}
          wrapperCol={{ span: 3 }}
          labelAlign="left"
          colon={false}
        >
          <Switch />
        </Form.Item>
      </Form>
      <NewAppModal
        data={newAppModal.data}
        open={newAppModal.visible}
        setOpen={(open) => newAppModal.toggle(open)}
        showImage={false}
        onSuccess={handleUpdateSuccess}
      />
      <RelateKnowledgeModal
        open={relateKnowledgeModal.visible}
        setOpen={(open) => relateKnowledgeModal.toggle(open)}
        onSubmit={handleRelateKnowledge}
      />
      {/* 暂时隐藏，后续再支持 */}
      {/* <PromptGenerationModal
        open={promptGenerationModal.visible}
        setOpen={(open) => promptGenerationModal.toggle(open)}
        onSubmit={handleApplyPrompt}
        llmId={systemSetting.llm_id || ""}
        llmOptions={allOptions[LlmModelType.Chat]}
      /> */}
    </div>
  );
}

export default AppConfigPanel;
