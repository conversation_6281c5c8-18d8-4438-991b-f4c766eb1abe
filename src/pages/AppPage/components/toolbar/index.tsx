import { DownOutlined } from "@ant-design/icons";
import { Dropdown } from "antd";

import { Badge, BadgeVariant } from "@/components/main/badge";
import { Button } from "@/components/main/button";
import { ClockIcon, TriangleIcon } from "@/components/main/icon";
import { convertUTCToLocalTimezone } from "@/utils/utils";

interface ToolbarProps {
  updateTime: string;
  hasSaved: boolean;
  onSave: (isPublish?: boolean) => void;
  onRun: () => void;
}

export default function Toolbar({
  updateTime,
  hasSaved,
  onSave,
  onRun,
}: ToolbarProps) {
  return (
    <div className="flex items-center justify-end gap-3">
      <div className="flex items-center gap-1">
        <ClockIcon className="text-base text-text-4" />
        <div className="line-clamp-1 break-all text-xs text-text-3">
          {convertUTCToLocalTimezone(updateTime)}
        </div>
      </div>
      <Badge
        variant={hasSaved ? BadgeVariant.SUCCESS : BadgeVariant.PROCESSING}
        showDot
      >
        {hasSaved ? "已保存" : "未保存"}
      </Badge>
      <Button variant="outline" size="sm" onClick={onRun}>
        <TriangleIcon className="text-base text-text-1" />
        运行
      </Button>
      <Dropdown.Button
        menu={{
          items: [{ label: "保存并发布", key: "saveAndPublish" }],
          onClick: ({ key }) => {
            if (key === "saveAndPublish") {
              onSave(true);
            }
          },
        }}
        className="dropdown-button w-auto"
        onClick={() => onSave()}
        icon={<DownOutlined />}
        size="small"
      >
        保存
      </Dropdown.Button>
    </div>
  );
}
