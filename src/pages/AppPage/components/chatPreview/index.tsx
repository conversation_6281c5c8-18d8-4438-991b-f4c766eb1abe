import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import Chat from "@/components/antd/chat";
import { Message, MessageStatus } from "@/components/antd/chat/entity";
import { EraseIcon, PauseIcon, VolumeIcon } from "@/components/main/icon";
import { useAIChat } from "@/controllers/API/queries/app/useAIChat";
import { useSpeechSynthesis } from "@/hooks/common/useSpeechSynthesis";
import { AppConfigFormData, ConversationDetail } from "@/types/app";
import { getColorFromString } from "@/utils/styleUtils";
import { cn } from "@/utils/utils";
import { transformConversationDetail, transformToAppData } from "../../utils";
import ChatInput from "../chatInput";

interface ChatPreviewProps {
  data?: AppConfigFormData;
  getCompleteData: () => AppConfigFormData | undefined;
  hideHeader?: boolean;
  customTitle?: string;
  isDebugger?: boolean;
  conversation?: ConversationDetail;
  conversationId?: string;
  onCreateConversation?: (conversationId: string) => void;
}

export default function ChatPreview({
  data,
  getCompleteData,
  hideHeader = false,
  customTitle,
  isDebugger,
  conversation,
  conversationId,
  onCreateConversation,
}: ChatPreviewProps) {
  const [value, setValue] = useState<string | undefined>();

  const chat = Chat.useChat();
  const { appId } = useParams();
  const speech = useSpeechSynthesis();
  const chatRequest = useAIChat();

  const chatData = chat.conversation.get()?.prompts || [];
  const color = getColorFromString(appId || "");

  useEffect(() => {
    if (chat.conversation) {
      chat.conversation.remove();
    }

    const conversationId = `conversation_${new Date().valueOf()}`;
    chat.conversation.create({ id: conversationId });

    if (conversation) {
      const chatData = transformConversationDetail(conversation);
      chat.conversation.update({ assistantId: conversation.conversation.id });

      chatData.forEach((item) => {
        chat.prompt.create({
          id: item.id,
          title: item.title,
          assistantId: item.assistantId,
        });
        item.answers.forEach((answer) => {
          chat.message.create(item.id, {
            id: answer.id,
            content: answer.content,
            assistantId: answer.assistantId as any,
            status: MessageStatus.DONE,
          });
        });
      });
    }
  }, [conversation]);

  const handleSubmit = (raw = value) => {
    const val = raw?.trim();
    const completeData = getCompleteData();
    if (!completeData || chat.loading() || !val) return;

    setValue("");
    const promptId = new Date().valueOf().toString();
    const messageId = (new Date().valueOf() + 1).toString();
    chat.prompt.create({ id: promptId, title: val });
    chat.message.create(promptId, { id: messageId, content: "" });

    const data = transformToAppData(completeData);

    chatRequest.mutate(
      {
        conversation_id: chat.conversation.get()?.assistantId,
        prompt: data.prompt,
        input_value: val,
        model: data.model,
        dataset: data.dataset,
        from_source: isDebugger ? "debugger" : "web-app",
        appId: data.id,
      },
      {
        onopen() {
          chat.start(promptId, messageId);
        },
        onmessage(data) {
          const conversationId = chat.conversation.get()?.assistantId;

          if (!conversationId && data.conversation_id) {
            chat.conversation.update({ assistantId: data.conversation_id });
          }
          chat.push(promptId, messageId, data.content);
        },
        onclose(data) {
          chat.close(promptId, messageId);
          if (!isDebugger && data?.conversation_id && !conversationId) {
            onCreateConversation?.(data.conversation_id);
          }
        },
      },
    );
  };

  const renderMessageIcons = (record: Message) => {
    const enabled = speech.isSupported && !!data?.textToSpeech;
    if (!enabled) return null;

    if (speech.data === record.id) {
      return (
        <PauseIcon
          className="cursor-pointer text-base text-text-2"
          onClick={() => speech.cancel()}
        />
      );
    }
    return (
      <VolumeIcon
        className="cursor-pointer text-text-2"
        onClick={() => {
          speech.speak(record.content, record.id);
        }}
      />
    );
  };

  const handleClearChat = () => {
    chat.conversation.remove();
    chat.conversation.create({ id: new Date().valueOf().toString() });
  };

  return (
    <div className="flex h-full flex-1 flex-col">
      {!hideHeader && (
        <div className="border-b-solid flex h-[46px] items-center justify-between border-b border-border-1 bg-bg-light-3 px-10 py-3 text-sm font-medium">
          <div>{customTitle || "调试预览"}</div>
          <EraseIcon
            className="cursor-pointer text-base text-text-2"
            onClick={handleClearChat}
          />
        </div>
      )}
      <div className="flex min-h-0 flex-1 flex-col justify-center">
        <div
          className={cn(
            "min-h-0 flex-1 overflow-y-auto",
            chatData.length ? "" : "hidden",
          )}
        >
          <Chat
            chat={chat}
            regenerate={false}
            messageIcons={renderMessageIcons}
          >
            <Chat.Content
              data={chatData}
              avatar={
                <div
                  className={cn(
                    "flex h-6 w-6 items-center justify-center rounded-sm text-sm leading-4",
                    color,
                  )}
                >
                  ✨
                </div>
              }
            />
          </Chat>
        </div>
        <div className="flex flex-col items-center justify-center gap-6 px-[40px] py-3">
          {chatData.length ? null : (
            <>
              <div className="flex flex-col items-center justify-center gap-2">
                <div className="text-[60px] leading-[60px]">✨</div>
                <div className="text- text-xl font-medium">{data?.name}</div>
              </div>
              <div className="max-w-[480px] text-sm text-text-2">
                {data?.openingStatement}
              </div>
            </>
          )}
          <div
            className={cn(
              "w-full",
              chatData.length ? "max-w-[800px]" : "max-w-[592px]",
            )}
          >
            <ChatInput
              value={value}
              onChange={setValue}
              onPressEnter={() => handleSubmit()}
              button={{
                disabled: chat.loading() || !value?.trim(),
              }}
              placeholder="Ask anything"
              onSubmit={handleSubmit}
              enableVoiceInput={!!data?.speechToText}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
