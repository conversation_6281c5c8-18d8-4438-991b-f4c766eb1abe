import "@xyflow/react/dist/style.css";
import { ConfigProvider } from "antd";
import { Suspense, useEffect } from "react";
import { RouterProvider } from "react-router-dom";
import { LoadingPage } from "./pages/LoadingPage";
import router from "./routes";
import { useDarkStore } from "./stores/darkStore";
import { antdThemeConfig } from "./utils/styleUtils";

import zhCN from "antd/locale/zh_CN";

export default function App() {
  const dark = useDarkStore((state) => state.dark);

  useEffect(() => {
    if (!dark) {
      document.getElementById("body")!.classList.remove("dark");
    } else {
      document.getElementById("body")!.classList.add("dark");
    }
  }, [dark]);

  return (
    <ConfigProvider locale={zhCN} theme={antdThemeConfig}>
      <Suspense fallback={<LoadingPage />}>
        <RouterProvider router={router} />
      </Suspense>
    </ConfigProvider>
  );
}
