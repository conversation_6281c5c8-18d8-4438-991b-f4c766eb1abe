import { type ReactNode } from "react";

import { cn } from "@/utils/utils";

type ListCardProps = {
  title: string;
  content: string;
  iconWrapperClass?: string;
  onClick?: () => void;
  renderIcon?: () => ReactNode;
  renderFooter?: () => ReactNode;
  renderDescription?: () => ReactNode;
  renderTopRight?: () => ReactNode;
};

export default function ListCard({
  title,
  content,
  iconWrapperClass,
  onClick,
  renderIcon,
  renderFooter,
  renderDescription,
  renderTopRight,
}: ListCardProps) {
  return (
    <div
      className="group/card col-span-1 flex h-[160px] cursor-pointer flex-col gap-2 rounded-xl border border-solid border-border-1 bg-white p-2 transition-shadow hover:shadow-[0px_6px_20px_1px_rgba(117,145,212,0.12)]"
      onClick={onClick}
    >
      <div className="flex gap-2">
        <div
          className={cn(
            `flex h-12 w-12 items-center justify-center rounded-lg bg-bg-light-3`,
            iconWrapperClass,
          )}
        >
          {renderIcon?.()}
        </div>
        <div className="flex flex-1 flex-col justify-center">
          <div
            className={cn(
              "text-sm font-medium leading-[22px]",
              renderDescription ? "line-clamp-1" : "line-clamp-2",
            )}
          >
            {title}
          </div>
          <div className="flex items-center text-xs leading-5 text-text-3">
            {renderDescription?.()}
          </div>
        </div>
        {renderTopRight?.()}
      </div>
      <div className="flex flex-1 flex-col">
        <div className="flex-1 pb-2 group-hover/card:pb-4">
          <div className="line-clamp-4 h-full text-xs leading-5 text-text-4 group-hover/card:line-clamp-2">
            {content}
          </div>
        </div>
        <div className="hidden group-hover/card:block">{renderFooter?.()}</div>
      </div>
    </div>
  );
}
