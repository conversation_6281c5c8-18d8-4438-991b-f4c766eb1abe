import { But<PERSON> } from "@/components/main/button";
import { LeftArrowIcon } from "@/components/main/icon";
import { useCustomNavigate } from "@/customization/hooks/use-custom-navigate";

interface SubPageLayoutProps {
  title: string;
  backPath: string;
  children: React.ReactNode;
  navRightContent?: React.ReactNode;
}

export default function SubPageLayout({
  title,
  backPath,
  children,
  navRightContent,
}: SubPageLayoutProps) {
  const navigate = useCustomNavigate();

  return (
    <div className="flex h-full flex-col gap-4">
      <div className="flex">
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="iconSm"
            onClick={() => navigate(backPath)}
          >
            <LeftArrowIcon className="text-text-2" />
          </Button>
          <div className="h-4 border-l-[1px] border-border-1"></div>
          <h1 className="text-lg font-semibold">{title}</h1>
        </div>
        <div className="flex-1">{navRightContent}</div>
      </div>
      <div className="min-h-0 flex-1">{children}</div>
    </div>
  );
}
