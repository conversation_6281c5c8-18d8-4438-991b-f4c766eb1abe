export interface App extends AppConfig {
  id: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export type CreateAppParams = Partial<AppConfig>;

export interface UpdateAppParams extends CreateAppParams {
  app_id: string;
}

export interface Conversation {
  id: string;
  name: string;
  createdAt: string;
  lastMessage?: string;
}

export interface AppConfigFormData {
  name: string;
  description: string;
  id: string;
  icon: string;
  model: string;
  modelSettings: { temperature: number; max_tokens: number };
  prompt: string;
  openingStatement: string;
  speechToText: boolean;
  textToSpeech: boolean;
  datasets: {
    id: string;
    name: string;
  }[];
}

export interface AppConfig {
  id: string;
  name: string;
  description: string;
  icon: string;
  model: {
    model_name: string;
    provider: string;
    model_settings: { temperature: number; max_tokens: number };
  };
  dataset: {
    datasets: {
      id: string;
      name: string;
    }[];
  };
  prompt: string;
  opening_statement: string;
  speech_to_text: boolean;
  text_to_speech: boolean;
}

export interface ConversationDetail {
  conversation: {
    created_at: string;
    id: string;
    name: string;
    updated_at: string;
  };
  messages: {
    content: string;
    id: string;
    role: string;
    sender: string;
    sender_name: string;
    timestamp: string;
  }[];
}

export interface ChatMessage {
  id: string;
  title: string;
  assistantId: string;
  answers: {
    id: string;
    content: string;
    assistantId: string;
  }[];
}
