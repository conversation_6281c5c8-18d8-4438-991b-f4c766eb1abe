export interface Organization {
  id: string;
  name: string;
  org_type: string;
  parent_id: string;
  children?: Organization[];
}

export interface OrganizationNode {
  id: string;
  name: string;
  children?: OrganizationNode[];
  isEditing?: boolean;
}

export interface OrganizationUserPageData {
  items: OrganizationUser[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface OrganizationUser {
  id: string;
  username: string;
  nickname: string;
  email: string;
  avatar: string | null;
  phone: string | null;
  is_active: boolean;
  is_superuser: boolean;
  status: string;
  create_at: string;
  updated_at: string;
  last_login_at: string | null;
  last_login_time: string | null;
  language: string;
  timezone: string;
  is_authenticated: string;
  is_anonymous: string;
  login_channel: string | null;
}

export interface UserFormData {
  nickname: string;
  email: string;
  organization_id: string;
  role: string;
  phone?: string;
  password?: string;
}
