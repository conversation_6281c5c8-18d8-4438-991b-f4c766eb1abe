<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organization & Tenant API 测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
        }
        
        .sidebar {
            width: 300px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            overflow-y: auto;
        }
        
        .sidebar h2 {
            margin-bottom: 20px;
            color: #ecf0f1;
            border-bottom: 2px solid #34495e;
            padding-bottom: 10px;
        }
        
        .api-item {
            background: #34495e;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .api-item:hover {
            background: #3498db;
            transform: translateX(5px);
        }
        
        .api-item.active {
            background: #3498db;
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
        }
        
        .api-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .api-method {
            font-size: 12px;
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            display: inline-block;
            margin-right: 10px;
        }
        
        .api-method.get {
            background: #27ae60;
        }
        
        .api-method.delete {
            background: #e74c3c;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .test-panel {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            max-width: 800px;
        }
        
        .panel-header {
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 15px;
            margin-bottom: 25px;
        }
        
        .panel-title {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .panel-description {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        
        .response-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #ecf0f1;
        }
        
        .response-header {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .response-content {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .loading {
            color: #f39c12;
        }
        
        .error {
            color: #e74c3c;
        }
        
        .success {
            color: #27ae60;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <h2>API 测试工具</h2>
        
        <div style="margin-bottom: 15px; font-size: 14px; color: #bdc3c7;">Organization API</div>
        
        <div class="api-item" data-api="create">
            <div class="api-title">
                <span class="api-method">POST</span>
                创建组织
            </div>
            <div>/organization/</div>
        </div>
        
        <div class="api-item" data-api="tree">
            <div class="api-title">
                <span class="api-method get">GET</span>
                获取组织树
            </div>
            <div>/organization/tree</div>
        </div>
        
        
        <div class="api-item" data-api="get-all-users">
            <div class="api-title">
                <span class="api-method get">GET</span>
                获取用户列表
            </div>
            <div>/organization/users</div>
        </div>
        
        <div style="margin: 20px 0 15px 0; font-size: 14px; color: #bdc3c7; border-top: 1px solid #34495e; padding-top: 15px;">Tenant API</div>
        
        <div class="api-item" data-api="create-user">
            <div class="api-title">
                <span class="api-method">POST</span>
                创建用户
            </div>
            <div>/tenant/users</div>
        </div>
        
        <div class="api-item" data-api="delete-user">
            <div class="api-title">
                <span class="api-method delete">DELETE</span>
                删除用户
            </div>
            <div>/tenant/users/{user_id}</div>
        </div>
        
        <div style="margin: 20px 0 15px 0; font-size: 14px; color: #bdc3c7; border-top: 1px solid #34495e; padding-top: 15px;">Permission API</div>
        
        <div class="api-item" data-api="get-permissions">
            <div class="api-title">
                <span class="api-method get">GET</span>
                获取资源权限
            </div>
            <div>/permissions/{resource_type}/{resource_id}/permissions</div>
        </div>
        
        <div class="api-item" data-api="update-permissions">
            <div class="api-title">
                <span class="api-method">POST</span>
                更新资源权限
            </div>
            <div>/permissions/{resource_type}/{resource_id}/permissions</div>
        </div>
        
        <div style="margin: 20px 0 15px 0; font-size: 14px; color: #bdc3c7; border-top: 1px solid #34495e; padding-top: 15px;">User API</div>
        
        <div class="api-item" data-api="user-profile">
            <div class="api-title">
                <span class="api-method get">GET</span>
                获取用户信息
            </div>
            <div>/user/info</div>
        </div>
    </div>
    
    <div class="main-content">
        <!-- 创建组织 -->
        <div id="create-panel" class="test-panel">
            <div class="panel-header">
                <div class="panel-title">创建组织</div>
                <div class="panel-description">POST /organization/</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">组织名称 *</label>
                <input type="text" id="create-name" class="form-input" placeholder="请输入组织名称">
            </div>
            
            <div class="form-group">
                <label class="form-label">父组织ID</label>
                <input type="text" id="create-parent-id" class="form-input" placeholder="可选，留空表示根组织">
            </div>
            
            <div class="form-group">
                <label class="form-label">组织描述</label>
                <textarea id="create-description" class="form-input form-textarea" placeholder="请输入组织描述"></textarea>
            </div>
            
            <div class="form-group">
                <label class="form-label">组织类型</label>
                <select id="create-type" class="form-input">
                    <option value="department">部门</option>
                    <option value="company">公司</option>
                    <option value="team">团队</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">排序顺序</label>
                <input type="number" id="create-sort" class="form-input" value="0">
            </div>
            
            <button class="btn" onclick="createOrganization()">创建组织</button>
            
            <div class="response-section">
                <div class="response-header">响应结果</div>
                <div id="create-response" class="response-content">等待请求...</div>
            </div>
        </div>
        
        <!-- 获取组织树 -->
        <div id="tree-panel" class="test-panel hidden">
            <div class="panel-header">
                <div class="panel-title">获取组织树</div>
                <div class="panel-description">GET /organization/tree</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">租户ID</label>
                <input type="text" id="tree-tenant-id" class="form-input" placeholder="可选，不填使用当前租户">
            </div>
            
            <div class="form-group">
                <label class="form-label">根组织ID</label>
                <input type="text" id="tree-root-id" class="form-input" placeholder="可选，指定根节点">
            </div>
            
            <button class="btn" onclick="getOrganizationTree()">获取组织树</button>
            
            <div class="response-section">
                <div class="response-header">响应结果</div>
                <div id="tree-response" class="response-content">等待请求...</div>
            </div>
        </div>
        
        <!-- 获取组织成员 -->
        <div id="members-panel" class="test-panel hidden">
            <div class="panel-header">
                <div class="panel-title">获取组织成员和子组织</div>
                <div class="panel-description">GET /organization/{org_id}/members</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">组织ID *</label>
                <input type="text" id="members-org-id" class="form-input" placeholder="请输入组织ID">
            </div>
            
            <button class="btn" onclick="getOrganizationMembers()">获取成员列表</button>
            
            <div class="response-section">
                <div class="response-header">响应结果</div>
                <div id="members-response" class="response-content">等待请求...</div>
            </div>
        </div>
        
        <!-- 获取组织用户 -->
        <div id="get-users-panel" class="test-panel hidden">
            <div class="panel-header">
                <div class="panel-title">获取组织用户</div>
                <div class="panel-description">GET /organization/{org_id}/users</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">组织ID *</label>
                <input type="text" id="users-org-id" class="form-input" placeholder="请输入组织ID">
            </div>
            
            <button class="btn" onclick="getOrganizationUsers()">获取用户列表</button>
            
            <div class="response-section">
                <div class="response-header">响应结果</div>
                <div id="users-response" class="response-content">等待请求...</div>
            </div>
        </div>
        
        
        <!-- 创建用户 -->
        <div id="create-user-panel" class="test-panel hidden">
            <div class="panel-header">
                <div class="panel-title">创建用户</div>
                <div class="panel-description">POST /tenant/users</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">姓名 *</label>
                <input type="text" id="user-nickname" class="form-input" placeholder="请输入用户姓名">
            </div>
            
            <div class="form-group">
                <label class="form-label">邮箱 *</label>
                <input type="email" id="user-email" class="form-input" placeholder="请输入邮箱地址">
            </div>
            
            <div class="form-group">
                <label class="form-label">手机号</label>
                <input type="tel" id="user-phone" class="form-input" placeholder="请输入手机号">
            </div>
            
            <div class="form-group">
                <label class="form-label">部门ID</label>
                <input type="text" id="user-org-id" class="form-input" placeholder="可选，指定用户所属部门">
            </div>
            
            <div class="form-group">
                <label class="form-label">租户角色</label>
                <select id="user-role" class="form-input">
                    <option value="normal">普通用户</option>
                    <option value="admin">管理员</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">密码</label>
                <input type="password" id="user-password" class="form-input" placeholder="留空则自动生成强密码">
            </div>
            
            <button class="btn" onclick="createUser()">创建用户</button>
            
            <div class="response-section">
                <div class="response-header">响应结果</div>
                <div id="create-user-response" class="response-content">等待请求...</div>
            </div>
        </div>
        
        <!-- 获取租户成员 -->
        <div id="tenant-members-panel" class="test-panel hidden">
            <div class="panel-header">
                <div class="panel-title">获取租户成员</div>
                <div class="panel-description">GET /tenant/my-tenant/members</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">页码</label>
                <input type="number" id="members-page" class="form-input" value="1" min="1">
            </div>
            
            <div class="form-group">
                <label class="form-label">每页数量</label>
                <input type="number" id="members-size" class="form-input" value="10" min="1" max="100">
            </div>
            
            <div class="form-group">
                <label class="form-label">搜索关键词</label>
                <input type="text" id="members-search" class="form-input" placeholder="搜索用户名/邮箱/昵称">
            </div>
            
            <div class="form-group">
                <label class="form-label">排序字段</label>
                <select id="members-order" class="form-input">
                    <option value="create_at">创建时间</option>
                    <option value="username">用户名</option>
                    <option value="email">邮箱</option>
                    <option value="updated_at">更新时间</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">排序方式</label>
                <select id="members-desc" class="form-input">
                    <option value="true">降序</option>
                    <option value="false">升序</option>
                </select>
            </div>
            
            <button class="btn" onclick="getTenantMembers()">获取成员列表</button>
            
            <div class="response-section">
                 <div class="response-header">响应结果</div>
                 <div id="tenant-members-response" class="response-content">等待请求...</div>
             </div>
         </div>
         
         <!-- 获取资源权限 -->
         <div id="get-permissions-panel" class="test-panel hidden">
             <div class="panel-header">
                 <div class="panel-title">获取资源权限</div>
                 <div class="panel-description">GET /permissions/{resource_type}/{resource_id}/permissions</div>
             </div>
             
             <div class="form-group">
                 <label class="form-label">资源类型 *</label>
                 <select id="get-perm-resource-type" class="form-input">
                     <option value="app">应用 (app)</option>
                     <option value="knowledgebase">知识库 (knowledgebase)</option>
                     <option value="workflow">工作流 (workflow)</option>
                 </select>
             </div>
             
             <div class="form-group">
                 <label class="form-label">资源ID *</label>
                 <input type="text" id="get-perm-resource-id" class="form-input" placeholder="请输入资源ID">
             </div>
             
             <button class="btn" onclick="getResourcePermissions()">获取权限列表</button>
             
             <div class="response-section">
                 <div class="response-header">响应结果</div>
                 <div id="get-permissions-response" class="response-content">等待请求...</div>
             </div>
         </div>
         
         <!-- 更新资源权限 -->
         <div id="update-permissions-panel" class="test-panel hidden">
             <div class="panel-header">
                 <div class="panel-title">更新资源权限 [BETA]</div>
                 <div class="panel-description">POST /permissions/{resource_type}/{resource_id}/permissions</div>
             </div>
             
             <div class="form-group">
                 <label class="form-label">资源类型 *</label>
                 <select id="update-perm-resource-type" class="form-input">
                     <option value="app">应用 (app)</option>
                     <option value="knowledgebase">知识库 (knowledgebase)</option>
                     <option value="workflow">工作流 (workflow)</option>
                 </select>
             </div>
             
             <div class="form-group">
                 <label class="form-label">资源ID *</label>
                 <input type="text" id="update-perm-resource-id" class="form-input" placeholder="请输入资源ID">
             </div>
             
             <div class="form-group">
                 <label class="form-label">权限范围</label>
                 <select id="permission-scope" class="form-input">
                     <option value="">请选择权限范围</option>
                     <option value="owner_only">只有我（所有者）</option>
                     <option value="organization">指定组织</option>
                     <option value="members">指定成员</option>
                     <option value="tenant_all">全租户</option>
                 </select>
             </div>
             
             <div class="form-group">
                 <label class="form-label">权限列表 (JSON格式) *</label>
                 <textarea id="permissions-json" class="form-input" rows="8" placeholder='[\n  {\n    "subject_type": "user",\n    "subject_id": "user-123",\n    "permission_type": "admin",\n    "expire_at": null\n  },\n  {\n    "subject_type": "organization",\n    "subject_id": "org-456",\n    "permission_type": "member",\n    "expire_at": "2024-12-31T23:59:59"\n  }\n]'></textarea>
             </div>
             
             <div style="margin-bottom: 15px; padding: 10px; background-color: #2c3e50; border-radius: 4px; font-size: 12px; color: #ecf0f1;">
                 <strong>权限类型说明：</strong><br>
                 • subject_type: user(用户) | organization(组织) | tenant(租户)<br>
                 • permission_type: admin(管理员) | member(成员) | viewer(查看者)<br>
                 • expire_at: 过期时间，null表示永不过期
             </div>
             
             <button class="btn" onclick="updateResourcePermissions()">更新权限</button>
             
             <div class="response-section">
                 <div class="response-header">响应结果</div>
                 <div id="update-permissions-response" class="response-content">等待请求...</div>
             </div>
         </div>
         
         <!-- 删除用户 -->
         <div id="delete-user-panel" class="test-panel hidden">
             <div class="panel-header">
                 <div class="panel-title">删除用户</div>
                 <div class="panel-description">DELETE /tenant/users/{user_id}</div>
             </div>
             
             <div class="form-group">
                 <label class="form-label">用户ID *</label>
                 <input type="text" id="delete-user-id" class="form-input" placeholder="请输入要删除的用户ID">
             </div>
             
             <div style="margin-bottom: 15px; padding: 10px; background-color: #f8d7da; border-radius: 4px; font-size: 14px; color: #721c24; border: 1px solid #f5c6cb;">
                 <strong>⚠️ 警告：</strong>删除用户操作不可逆，请谨慎操作！
             </div>
             
             <button class="btn" onclick="deleteUser()" style="background: #e74c3c;">删除用户</button>
             
             <div class="response-section">
                 <div class="response-header">响应结果</div>
                 <div id="delete-user-response" class="response-content">等待请求...</div>
             </div>
         </div>
         
         <!-- 获取用户列表 -->
        <div id="get-all-users-panel" class="test-panel hidden">
            <div class="panel-header">
                <div class="panel-title">获取用户列表</div>
                <div class="panel-description">GET /organization/users</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">组织ID（可选）</label>
                <input type="text" id="all-users-org-id" class="form-input" placeholder="不填写则返回租户下所有成员">
            </div>
            
            <div class="form-group">
                <label class="form-label">页码</label>
                <input type="number" id="all-users-page" class="form-input" value="1" min="1">
            </div>
            
            <div class="form-group">
                <label class="form-label">每页数量</label>
                <input type="number" id="all-users-size" class="form-input" value="10" min="1" max="100">
            </div>
            
            <div class="form-group">
                <label class="form-label">搜索关键词</label>
                <input type="text" id="all-users-search" class="form-input" placeholder="搜索用户名/邮箱/昵称">
            </div>
            
            <div class="form-group">
                <label class="form-label">排序字段</label>
                <select id="all-users-order" class="form-input">
                    <option value="create_time">创建时间</option>
                    <option value="updated_at">更新时间</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">排序方式</label>
                <select id="all-users-desc" class="form-input">
                    <option value="true">降序</option>
                    <option value="false">升序</option>
                </select>
            </div>
            
            <button class="btn" onclick="getAllUsers()">获取用户列表</button>
            
            <div class="response-section">
                <div class="response-header">响应结果</div>
                <div id="get-all-users-response" class="response-content">等待请求...</div>
            </div>
        </div>
         
         <!-- 获取用户信息 -->
         <div id="user-profile-panel" class="test-panel hidden">
             <div class="panel-header">
                 <div class="panel-title">获取用户信息</div>
                 <div class="panel-description">GET /user/info</div>
             </div>
             
             <div style="margin-bottom: 15px; padding: 10px; background-color: #ecf0f1; border-radius: 4px; font-size: 14px; color: #2c3e50;">
                 <strong>说明：</strong>此接口不需要任何参数，直接获取当前用户的详细信息。
             </div>
             
             <button class="btn" onclick="getUserProfile()">获取用户信息</button>
             
             <div class="response-section">
                 <div class="response-header">响应结果</div>
                 <div id="user-profile-response" class="response-content">等待请求...</div>
             </div>
         </div>
     </div>
    
    <script>
        const BASE_URL = 'http://localhost:3000/api/v3';
        
        // 切换API面板
        function showPanel(apiType) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.test-panel');
            panels.forEach(panel => panel.classList.add('hidden'));
            
            // 移除所有活跃状态
            const items = document.querySelectorAll('.api-item');
            items.forEach(item => item.classList.remove('active'));
            
            // 显示对应面板
            const targetPanel = document.getElementById(apiType + '-panel');
            if (targetPanel) {
                targetPanel.classList.remove('hidden');
            }
            
            // 设置当前项为活跃状态
            const currentItem = document.querySelector(`[data-api="${apiType}"]`);
            if (currentItem) {
                currentItem.classList.add('active');
            }
        }
        
        // 处理API项目点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const apiItems = document.querySelectorAll('.api-item[data-api]');
            apiItems.forEach(item => {
                item.addEventListener('click', function() {
                    const apiType = this.getAttribute('data-api');
                    showPanel(apiType);
                });
            });
            
            // 默认激活第一个
            const firstItem = document.querySelector('.api-item[data-api]');
            if (firstItem) {
                firstItem.click();
            }
        });
        

        
        // 创建组织
        async function createOrganization() {
            const responseEl = document.getElementById('create-response');
            responseEl.textContent = '请求中...';
            responseEl.className = 'response-content loading';
            
            const data = {
                name: document.getElementById('create-name').value,
                description: document.getElementById('create-description').value || null,
                org_type: document.getElementById('create-type').value,
                sort_order: parseInt(document.getElementById('create-sort').value) || 0
            };
            
            const parentId = document.getElementById('create-parent-id').value;
            if (parentId) {
                data.parent_id = parentId;
            }
            
            try {
                const response = await fetch(`${BASE_URL}/organization/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                responseEl.textContent = JSON.stringify(result, null, 2);
                responseEl.className = response.ok ? 'response-content success' : 'response-content error';
            } catch (error) {
                responseEl.textContent = `请求失败: ${error.message}`;
                responseEl.className = 'response-content error';
            }
        }
        
        // 获取组织树
        async function getOrganizationTree() {
            const responseEl = document.getElementById('tree-response');
            responseEl.textContent = '请求中...';
            responseEl.className = 'response-content loading';
            
            const params = new URLSearchParams();
            const tenantId = document.getElementById('tree-tenant-id').value;
            const rootId = document.getElementById('tree-root-id').value;
            
            if (tenantId) params.append('tenant_id', tenantId);
            if (rootId) params.append('root_id', rootId);
            
            const queryString = params.toString();
            const url = `${BASE_URL}/organization/tree${queryString ? '?' + queryString : ''}`;
            
            try {
                const response = await fetch(url);
                const result = await response.json();
                responseEl.textContent = JSON.stringify(result, null, 2);
                responseEl.className = response.ok ? 'response-content success' : 'response-content error';
            } catch (error) {
                responseEl.textContent = `请求失败: ${error.message}`;
                responseEl.className = 'response-content error';
            }
        }
        
        // 获取组织成员
        async function getOrganizationMembers() {
            const responseEl = document.getElementById('members-response');
            responseEl.textContent = '请求中...';
            responseEl.className = 'response-content loading';
            
            const orgId = document.getElementById('members-org-id').value;
            if (!orgId) {
                responseEl.textContent = '请输入组织ID';
                responseEl.className = 'response-content error';
                return;
            }
            
            try {
                const response = await fetch(`${BASE_URL}/organization/${orgId}/members`);
                const result = await response.json();
                responseEl.textContent = JSON.stringify(result, null, 2);
                responseEl.className = response.ok ? 'response-content success' : 'response-content error';
            } catch (error) {
                responseEl.textContent = `请求失败: ${error.message}`;
                responseEl.className = 'response-content error';
            }
        }
        
        // 获取组织用户
        async function getOrganizationUsers() {
            const responseEl = document.getElementById('users-response');
            responseEl.textContent = '请求中...';
            responseEl.className = 'response-content loading';
            
            const orgId = document.getElementById('users-org-id').value;
            if (!orgId) {
                responseEl.textContent = '请输入组织ID';
                responseEl.className = 'response-content error';
                return;
            }
            
            try {
                const response = await fetch(`${BASE_URL}/organization/${orgId}/users`);
                const result = await response.json();
                responseEl.textContent = JSON.stringify(result, null, 2);
                responseEl.className = response.ok ? 'response-content success' : 'response-content error';
            } catch (error) {
                responseEl.textContent = `请求失败: ${error.message}`;
                responseEl.className = 'response-content error';
            }
        }
        
        // 创建用户
        async function createUser() {
            const responseEl = document.getElementById('create-user-response');
            responseEl.textContent = '请求中...';
            responseEl.className = 'response-content loading';
            
            const data = {
                nickname: document.getElementById('user-nickname').value,
                email: document.getElementById('user-email').value,
                role: document.getElementById('user-role').value
            };
            
            // 可选字段
            const phone = document.getElementById('user-phone').value;
            const orgId = document.getElementById('user-org-id').value;
            const password = document.getElementById('user-password').value;
            
            if (phone) data.phone = phone;
            if (orgId) data.organization_id = orgId;
            if (password) data.password = password;
            
            if (!data.nickname || !data.email) {
                responseEl.textContent = '请填写必填字段：姓名和邮箱';
                responseEl.className = 'response-content error';
                return;
            }
            
            try {
                const response = await fetch(`${BASE_URL}/tenant/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                responseEl.textContent = JSON.stringify(result, null, 2);
                responseEl.className = response.ok ? 'response-content success' : 'response-content error';
            } catch (error) {
                responseEl.textContent = `请求失败: ${error.message}`;
                responseEl.className = 'response-content error';
            }
        }
        
        // 获取租户成员
        async function getTenantMembers() {
            const responseEl = document.getElementById('tenant-members-response');
            responseEl.textContent = '请求中...';
            responseEl.className = 'response-content loading';
            
            const params = new URLSearchParams();
            const page = document.getElementById('members-page').value;
            const size = document.getElementById('members-size').value;
            const search = document.getElementById('members-search').value;
            const orderBy = document.getElementById('members-order').value;
            const desc = document.getElementById('members-desc').value;
            
            if (page) params.append('page', page);
            if (size) params.append('size', size);
            if (search) params.append('search', search);
            if (orderBy) params.append('order_by', orderBy);
            if (desc) params.append('desc', desc);
            
            const queryString = params.toString();
            const url = `${BASE_URL}/tenant/my-tenant/members${queryString ? '?' + queryString : ''}`;
            
            try {
                const response = await fetch(url);
                const result = await response.json();
                responseEl.textContent = JSON.stringify(result, null, 2);
                responseEl.className = response.ok ? 'response-content success' : 'response-content error';
            } catch (error) {
                 responseEl.textContent = `请求失败: ${error.message}`;
                 responseEl.className = 'response-content error';
             }
         }
         
         // 获取资源权限
         async function getResourcePermissions() {
             const responseEl = document.getElementById('get-permissions-response');
             responseEl.textContent = '请求中...';
             responseEl.className = 'response-content loading';
             
             const resourceType = document.getElementById('get-perm-resource-type').value;
             const resourceId = document.getElementById('get-perm-resource-id').value;
             
             if (!resourceType || !resourceId) {
                 responseEl.textContent = '请填写必填字段：资源类型和资源ID';
                 responseEl.className = 'response-content error';
                 return;
             }
             
             try {
                 const response = await fetch(`${BASE_URL}/permissions/${resourceType}/${resourceId}/permissions`);
                 const result = await response.json();
                 responseEl.textContent = JSON.stringify(result, null, 2);
                 responseEl.className = response.ok ? 'response-content success' : 'response-content error';
             } catch (error) {
                 responseEl.textContent = `请求失败: ${error.message}`;
                 responseEl.className = 'response-content error';
             }
         }
         
         // 更新资源权限
         async function updateResourcePermissions() {
             const responseEl = document.getElementById('update-permissions-response');
             responseEl.textContent = '请求中...';
             responseEl.className = 'response-content loading';
             
             const resourceType = document.getElementById('update-perm-resource-type').value;
             const resourceId = document.getElementById('update-perm-resource-id').value;
             const permissionScope = document.getElementById('permission-scope').value;
             const permissionsJsonStr = document.getElementById('permissions-json').value;
             
             if (!resourceType || !resourceId || !permissionsJsonStr) {
                 responseEl.textContent = '请填写必填字段：资源类型、资源ID和权限列表';
                 responseEl.className = 'response-content error';
                 return;
             }
             
             let permissions;
             try {
                 permissions = JSON.parse(permissionsJsonStr);
             } catch (e) {
                 responseEl.textContent = '权限列表JSON格式错误，请检查格式';
                 responseEl.className = 'response-content error';
                 return;
             }
             
             const data = {
                 permissions: permissions
             };
             
             if (permissionScope) {
                 data.permission_scope = permissionScope;
             }
             
             try {
                 const response = await fetch(`${BASE_URL}/permissions/${resourceType}/${resourceId}/permissions`, {
                     method: 'POST',
                     headers: {
                         'Content-Type': 'application/json',
                     },
                     body: JSON.stringify(data)
                 });
                 
                 const result = await response.json();
                 responseEl.textContent = JSON.stringify(result, null, 2);
                 responseEl.className = response.ok ? 'response-content success' : 'response-content error';
             } catch (error) {
                 responseEl.textContent = `请求失败: ${error.message}`;
                 responseEl.className = 'response-content error';
             }
         }
         
         // 删除用户
         async function deleteUser() {
             const responseEl = document.getElementById('delete-user-response');
             responseEl.textContent = '请求中...';
             responseEl.className = 'response-content loading';
             
             const userId = document.getElementById('delete-user-id').value;
             if (!userId) {
                 responseEl.textContent = '请输入用户ID';
                 responseEl.className = 'response-content error';
                 return;
             }
             
             try {
                 const response = await fetch(`${BASE_URL}/tenant/users/${userId}`, {
                     method: 'DELETE'
                 });
                 
                 const result = await response.json();
                 responseEl.textContent = JSON.stringify(result, null, 2);
                 responseEl.className = response.ok ? 'response-content success' : 'response-content error';
             } catch (error) {
                 responseEl.textContent = `请求失败: ${error.message}`;
                 responseEl.className = 'response-content error';
             }
         }
         
         // 获取用户列表
         async function getAllUsers() {
             const responseEl = document.getElementById('get-all-users-response');
             responseEl.textContent = '请求中...';
             responseEl.className = 'response-content loading';
             
             const params = new URLSearchParams();
             const orgId = document.getElementById('all-users-org-id').value;
             const page = document.getElementById('all-users-page').value;
             const size = document.getElementById('all-users-size').value;
             const search = document.getElementById('all-users-search').value;
             const orderBy = document.getElementById('all-users-order').value;
             const desc = document.getElementById('all-users-desc').value;
             
             if (orgId) params.append('org_id', orgId);
             if (page) params.append('page', page);
             if (size) params.append('size', size);
             if (search) params.append('search', search);
             if (orderBy) params.append('order_by', orderBy);
             if (desc) params.append('desc', desc);
             
             const queryString = params.toString();
             const url = `${BASE_URL}/organization/users${queryString ? '?' + queryString : ''}`;
             
             try {
                 const response = await fetch(url);
                 const result = await response.json();
                 responseEl.textContent = JSON.stringify(result, null, 2);
                 responseEl.className = response.ok ? 'response-content success' : 'response-content error';
             } catch (error) {
                 responseEl.textContent = `请求失败: ${error.message}`;
                 responseEl.className = 'response-content error';
             }
         }
         
         // 获取用户信息
         async function getUserProfile() {
             const responseEl = document.getElementById('user-profile-response');
             responseEl.textContent = '请求中...';
             responseEl.className = 'response-content loading';
             
             try {
                 const response = await fetch(`${BASE_URL}/user/info`);
                 const result = await response.json();
                 responseEl.textContent = JSON.stringify(result, null, 2);
                 responseEl.className = response.ok ? 'response-content success' : 'response-content error';
             } catch (error) {
                 responseEl.textContent = `请求失败: ${error.message}`;
                 responseEl.className = 'response-content error';
             }
         }
     </script>
</body>
</html>